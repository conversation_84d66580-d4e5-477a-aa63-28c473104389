import time
import random
import requests
import socket
import logging
import concurrent.futures
from bs4 import BeautifulSoup
import re
from threading import Thread, Lock, Condition
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from logger_helper import setup_logger # Import setup_logger


class ProxyHandler:
    def __init__(self, log_level=logging.INFO, startSearching=False, log_file='proxy_handler.log'):
        # Initialize logger using logger_helper
        self.logger = setup_logger(__name__, log_file=log_file, level=log_level)

        self.proxy_list = [] # proxy_list now stores tuples of (source_url, proxy_string)
        self.bad_proxies = set()
        self.bad_proxies_lock = Lock()
        self.valid_proxies = []
        self.consumed_proxies = []
        self.searching_thread = None
        self.fetch_lock = Lock()
        self.condition = Condition()
        self.source_stats = {}
        self.direct_attempts = 0
        self.proxy_attempts = 0
        self.session = requests.Session()
        # Configure session with browser-like headers
        self.session.headers.update({
            'accept': 'text/css,*/*;q=0.1',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'en-US,en;q=0.9',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'sec-ch-ua-arch': '"x86"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-full-version': '"139.0.7258.139"',
            'sec-ch-ua-full-version-list': '"Not;A=Brand";v="********", "Google Chrome";v="139.0.7258.139", "Chromium";v="139.0.7258.139"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua-platform-version': '"10.0.0"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'content-type': 'text/plain'
        })
        self.last_direct_use_time = 0
        self.seen_proxies = set() # Initialize a set to track seen proxies


        logging.getLogger("urllib3").setLevel(logging.WARNING)

        if startSearching:
            self.start_searching()

    def get_with_proxy(self, url, headers=None, abort_if_timeout=False, method="GET", log_enabled=True):
        # Always try a direct request first as the primary method
        response = self.direct_request(url, headers, method, log_enabled)
        if response is not None:
            return response

        # Only try proxies if direct request fails
        attempts = 0
        while attempts < 10:
            proxy_tuple = self.fetch_proxy() # fetch_proxy now returns a tuple (source_url, proxy_string)
            if not proxy_tuple:
                time.sleep(5)
                # Try direct request again as fallback
                response = self.direct_request(url, headers, method, log_enabled)
                if response is not None:
                    return response
            else:
                source_url, proxy_str = proxy_tuple
                protocol, address = proxy_str.split("://")[0] if "://" in proxy_str else "http", proxy_str

                proxy_dict = {protocol: address}
                try:
                    # Use the session for proxy requests to ensure consistent headers
                    response = self.session.request(method, url, headers=headers, proxies=proxy_dict, timeout=3, verify=False)
                    if response.status_code == 200:
                        if method != "HEAD" and log_enabled:
                            self.proxy_attempts += 1
                            self.logger.info(
                                f"Accessed {url} via proxy from {source_url} after {self.direct_attempts} direct hits and {self.proxy_attempts} proxy hits, proxy count: {len(self.valid_proxies)}"
                            )
                        return response
                    else:
                        self.logger.warning(f"Proxy request to {url} failed with status code {response.status_code} using proxy from {source_url}")
                except requests.RequestException as e:
                    if isinstance(e, requests.Timeout) and abort_if_timeout and attempts >= 2:
                        self.logger.error(f"{url} - aborted due to timeout on third attempt with abort_if_timeout enabled.")
                        # Try direct request one last time before giving up
                        response = self.direct_request(url, headers, method, log_enabled)
                        if response is not None:
                            return response
                        return None
                    self.logger.warning(f"Proxy request to {url} failed: {e}")

                with self.bad_proxies_lock:
                    self.bad_proxies.add(proxy_str)

                attempts += 1
                # Try direct request as fallback after each proxy failure
                response = self.direct_request(url, headers, method, log_enabled)
                if response is not None:
                    return response
            time.sleep(5)
        self.logger.error(f"{url} - exceeded maximum proxy attempts (10). Trying direct request one last time.")
        # Final attempt with direct request before giving up
        response = self.direct_request(url, headers, method, log_enabled)
        return response

    def direct_request(self, url, headers, method, log_enabled):
        try:
            direct_response = self.session.request(method, url, headers=headers, timeout=3, verify=False)
            if direct_response.status_code == 200:
                if method != "HEAD" and log_enabled:
                    self.direct_attempts += 1
                    self.logger.info(
                        f"Accessed {url} directly after {self.direct_attempts} direct hits and {self.proxy_attempts} proxy hits, proxy count: {len(self.valid_proxies)}"
                    )
                self.last_direct_use_time = time.time() # Update time on SUCCESS
                return direct_response
            else:
                self.logger.warning(f"Direct request to {url} failed with status code {direct_response.status_code}")
        except requests.RequestException as e:
            self.logger.warning(f"Direct request to {url} failed: {e}")
            self.last_direct_use_time = time.time() # Update time on FAILURE
        return None


    def fetch_proxy(self):
        start_wait_time = time.time()
        proxy_tuple = None # fetch_proxy now returns a tuple

        with self.condition:
            while not self.valid_proxies and (self.proxy_list or (self.searching_thread and self.searching_thread.is_alive())):
                self.logger.info("Waiting for valid proxies...")
                if not self.condition.wait(timeout=5):
                    self.logger.warning("Timed out waiting for valid proxies.")
                    break

        if not self.valid_proxies and self.consumed_proxies:
            with self.fetch_lock:
                # Refilling valid_proxies with previously consumed proxies
                self.valid_proxies = [proxy for proxy in self.consumed_proxies if proxy[1] not in self.bad_proxies] # check proxy string in bad_proxies
                self.logger.info(f"no valid proxies remained, using consumed proxies now: {len(self.valid_proxies)}")
                self.consumed_proxies.clear()

        if self.valid_proxies:
            with self.fetch_lock:
                proxy_tuple = self.valid_proxies.pop(0) # pop a tuple (source_url, proxy_string)
                self.consumed_proxies.append(proxy_tuple) # append the tuple

        if proxy_tuple:
            source_url, proxy_str = proxy_tuple
            protocol = proxy_str.split("://")[0] if "://" in proxy_str else "http"
            address = proxy_str
            return proxy_tuple # return the tuple


        self.logger.error("No valid proxies found after timeout.")
        return None

    def start_searching(self):
        if not self.searching_thread or not self.searching_thread.is_alive():
            self.searching_thread = Thread(target=self._search_proxies)
            self.searching_thread.daemon = True
            self.searching_thread.start()

    def _search_proxies(self):
        self.logger.debug("Starting background proxy search...")
        batch_size = 50
        try:
            while True:
                try:
                    if len(self.valid_proxies) < 200:
                        self.logger.debug("valid_proxies below threshold (200), starting validation. Current count: %d", len(self.valid_proxies))

                        if not self.proxy_list:
                            self.logger.info("Proxy list empty, attempting to fetch new proxies.")
                            try:
                                self.fetch_proxies()
                                self.logger.info("Fetched new proxies, count: %d", len(self.proxy_list))
                            except Exception as e:
                                self.logger.error("Error fetching proxies: %s", e)
                                time.sleep(5)
                                continue

                        proxies_to_validate = [self.proxy_list.pop(0) for _ in range(min(batch_size, len(self.proxy_list)))]
                        self.logger.debug("Validating a batch of %d proxies...", len(proxies_to_validate))

                        with ThreadPoolExecutor(max_workers=10) as executor:
                            futures = {executor.submit(self.validate_proxy, proxy_tuple): proxy_tuple for proxy_tuple in proxies_to_validate} # Pass the tuple

                            for future in as_completed(futures):
                                proxy_tuple = futures[future]
                                try:
                                    result = future.result()
                                    if result:
                                        with self.condition:
                                            self.valid_proxies.append(proxy_tuple) # Append the tuple
                                            self.condition.notify()

                                except Exception as e:
                                    source_url, proxy_str = proxy_tuple
                                    self.logger.error("Error processing proxy %s from %s: %s", proxy_str, source_url, e)

                except Exception as outer_loop_exception:
                    self.logger.error("Unexpected error in proxy search loop: %s", outer_loop_exception)
                    time.sleep(5)

        except Exception as fatal_exception:
            self.logger.critical("Fatal error in _search_proxies, terminating search: %s", fatal_exception)


    def check_all_proxies(self):
        self.logger.info("Fetching and checking all proxies in the pool...")
        if not self.proxy_list:
            self.fetch_proxies()
        self.last_direct_accessed_time = time.time()

        processed_count = {}

        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = {}

            for source_url, proxy in self.proxy_list: # unpack tuple
                if processed_count.get(source_url, 0) >= 500:
                    continue

                processed_count[source_url] = processed_count.get(source_url, 0) + 1
                future = executor.submit(self.validate_proxy, (source_url, proxy)) # pass tuple
                futures[future] = (source_url, proxy)

            for future in concurrent.futures.as_completed(futures):
                source_url, proxy = futures[future] # unpack tuple again

                result = future.result()
                if result:
                    self.source_stats[source_url]['good'] += 1
                    self.logger.info(f"Valid proxy: {proxy} from {source_url}")
                else:
                    self.source_stats[source_url]['bad'] += 1
                    self.logger.debug(f"Invalid proxy: {proxy} from {source_url}")

        self.log_summary()

    def log_summary(self):
        sorted_stats = sorted(
            self.source_stats.items(),
            key=lambda item: (item[1]['good'] / (item[1]['good'] + item[1]['bad'])) if (item[1]['good'] + item[1]['bad']) > 0 else 0,
            reverse=True
        )
        self.logger.info("Proxy Validation Summary:")

        good_pool_sources = []
        bad_pool_sources = []

        for url, stats in sorted_stats:
            total_checked = stats['good'] + stats['bad']
            success_rate = (stats['good'] / total_checked * 100) if total_checked > 0 else 0
            log_line = f'"{url}", # | Fetched: {stats["fetched"]} | Good: {stats["good"]} | Bad: {stats["bad"]} | Success Rate: {success_rate:.2f}%'
            if success_rate > 5:
                good_pool_sources.append(log_line)
            else:
                bad_pool_sources.append(log_line)

        if good_pool_sources:
            self.logger.info("\n# good pool")
            for line in good_pool_sources:
                self.logger.info(line)

        if bad_pool_sources:
            self.logger.info("\n\n# bad pool")
            for line in bad_pool_sources:
                self.logger.info("# " + line)


    def fetch_proxies(self):
        self.logger.debug("Fetching proxies from predefined sources...")
        urls = [
# good pool
"https://github.com/zloi-user/hideip.me/blob/master/https.txt", # | Fetched: 27477 | Good: 480 | Bad: 20 | Success Rate: 96.00%
"https://github.com/Vann-Dev/proxy-list/blob/main/proxies/https.txt", # | Fetched: 100 | Good: 84 | Bad: 16 | Success Rate: 84.00%
"https://github.com/zenjahid/FreeProxy4u/blob/main/socks4.txt", # | Fetched: 27202 | Good: 239 | Bad: 261 | Success Rate: 47.80%
"https://github.com/zenjahid/FreeProxy4u/blob/main/socks5.txt", # | Fetched: 35233 | Good: 199 | Bad: 301 | Success Rate: 39.80%
"https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/socks5.txt", # | Fetched: 3 | Good: 1 | Bad: 2 | Success Rate: 33.33%
"https://github.com/hookzof/socks5_list/blob/master/proxy.txt", # | Fetched: 92 | Good: 28 | Bad: 64 | Success Rate: 30.43%
"https://github.com/zloi-user/hideip.me/blob/master/socks5.txt", # | Fetched: 162 | Good: 47 | Bad: 115 | Success Rate: 29.01%
"https://github.com/ErcinDedeoglu/proxies/blob/main/proxies/socks4.txt", # | Fetched: 8712 | Good: 133 | Bad: 367 | Success Rate: 26.60%
"https://api.proxyscrape.com/v2/?request=getproxies&protocol=http&timeout=2000&country=all&simplified=true", # | Fetched: 154 | Good: 27 | Bad: 127 | Success Rate: 17.53%
"https://raw.githubusercontent.com/fyvri/fresh-proxy-list/archive/storage/classic/https.txt", # | Fetched: 10365 | Good: 86 | Bad: 414 | Success Rate: 17.20%
"https://github.com/zloi-user/hideip.me/blob/master/socks4.txt", # | Fetched: 187 | Good: 31 | Bad: 156 | Success Rate: 16.58%
"https://raw.githubusercontent.com/fyvri/fresh-proxy-list/archive/storage/classic/socks4.txt", # | Fetched: 14772 | Good: 80 | Bad: 420 | Success Rate: 16.00%
"https://github.com/saisuiu/Lionkings-Http-Proxys-Proxies/blob/main/cnfree.txt", # | Fetched: 442 | Good: 68 | Bad: 374 | Success Rate: 15.38%
"https://raw.githubusercontent.com/fyvri/fresh-proxy-list/archive/storage/classic/socks5.txt", # | Fetched: 13462 | Good: 57 | Bad: 443 | Success Rate: 11.40%
"https://github.com/zloi-user/hideip.me/blob/master/http.txt", # | Fetched: 683 | Good: 31 | Bad: 469 | Success Rate: 6.20%
"https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/http.txt", # | Fetched: 2396 | Good: 27 | Bad: 473 | Success Rate: 5.40%
"https://www.free-proxy-list.net/", # | Fetched: 114 | Good: 6 | Bad: 108 | Success Rate: 5.26%


# bad pool
# "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/socks4.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
# "https://github.com/javadbazokar/PROXY-List/blob/main/https.txt", # | Fetched: 8 | Good: 0 | Bad: 8 | Success Rate: 0.00%
# "https://proxylist.geonode.com/api/proxy-list?limit=500&page=1&sort_by=lastChecked&sort_type=desc", # | Fetched: 23 | Good: 0 | Bad: 23 | Success Rate: 0.00%
# "https://github.com/TuanMinPay/live-proxy/blob/master/socks4.txt", # | Fetched: 1 | Good: 0 | Bad: 1 | Success Rate: 0.00%
# "https://raw.githubusercontent.com/dinoz0rg/proxy-list/main/scraped_proxies/socks5.txt", # | Fetched: 9 | Good: 0 | Bad: 9 | Success Rate: 0.00%
# "https://spys.me/socks.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
# "https://github.com/TuanMinPay/live-proxy/blob/master/socks5.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"http://spys.me/proxy.txt", # | Fetched: 400 | Good: 13 | Bad: 387 | Success Rate: 3.25%
#"https://github.com/ErcinDedeoglu/proxies/blob/main/proxies/http.txt", # | Fetched: 7774 | Good: 12 | Bad: 488 | Success Rate: 2.40%
#"https://github.com/javadbazokar/PROXY-List/blob/main/http.txt", # | Fetched: 59121 | Good: 8 | Bad: 492 | Success Rate: 1.60%
#"https://github.com/dpangestuw/Free-Proxy/blob/main/All_proxies.txt", # | Fetched: 2430 | Good: 2 | Bad: 498 | Success Rate: 0.40%
#"https://github.com/TuanMinPay/live-proxy/blob/master/http.txt", # | Fetched: 4648 | Good: 2 | Bad: 498 | Success Rate: 0.40%
#"https://raw.githubusercontent.com/proxifly/free-proxy-list/main/proxies/all/data.txt", # | Fetched: 11553 | Good: 1 | Bad: 499 | Success Rate: 0.20%
#"https://raw.githubusercontent.com/jetkai/proxy-list/main/online-proxies/txt/proxies.txt", # | Fetched: 4046 | Good: 1 | Bad: 499 | Success Rate: 0.20%
#"https://github.com/saisuiu/Lionkings-Http-Proxys-Proxies/blob/main/free.txt", # | Fetched: 1056 | Good: 1 | Bad: 499 | Success Rate: 0.20%
#"https://raw.githubusercontent.com/fyvri/fresh-proxy-list/archive/storage/classic/http.txt", # | Fetched: 227248 | Good: 1 | Bad: 499 | Success Rate: 0.20%
#"https://raw.githubusercontent.com/dinoz0rg/proxy-list/main/scraped_proxies/socks4.txt", # | Fetched: 1 | Good: 0 | Bad: 1 | Success Rate: 0.00%
#"https://www.proxy-list.download/api/v1/get?type=socks5", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://www.proxy-list.download/api/v1/get?type=socks4", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://rootjazz.com/proxies/proxies.txt", # | Fetched: 1162 | Good: 0 | Bad: 500 | Success Rate: 0.00%
#"https://www.proxynova.com/proxy-server-list/", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://proxiware.com/free-proxy-list", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://hide.mn/en/proxy-list/", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://freeproxylist.ru/", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://www.proxy-list.download/api/v1/get?type=http", # | Fetched: 90 | Good: 0 | Bad: 90 | Success Rate: 0.00%
#"https://github.com/jepluk/PROXYLIST/blob/main/all.json", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/ErcinDedeoglu/proxies/blob/main/proxies/https.txt", # | Fetched: 4 | Good: 0 | Bad: 4 | Success Rate: 0.00%
#"https://github.com/ErcinDedeoglu/proxies/blob/main/proxies/socks5.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/roosterkid/openproxylist/blob/main/HTTPS.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/roosterkid/openproxylist/blob/main/SOCKS4.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/roosterkid/openproxylist/blob/main/SOCKS5.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/proxifly/free-proxy-list/blob/main/proxies/all/data.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/vakhov/fresh-proxy-list/blob/master/http.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/vakhov/fresh-proxy-list/blob/master/https.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/vakhov/fresh-proxy-list/blob/master/socks4.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/vakhov/fresh-proxy-list/blob/master/socks5.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://cdn.rei.my.id/proxy/pALL", # | Fetched: 7 | Good: 0 | Bad: 7 | Success Rate: 0.00%
#"https://raw.githubusercontent.com/officialputuid/KangProxy/KangProxy/socks5/socks5.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://raw.githubusercontent.com/officialputuid/KangProxy/KangProxy/socks4/socks4.txt", # | Fetched: 5 | Good: 0 | Bad: 5 | Success Rate: 0.00%
#"https://raw.githubusercontent.com/officialputuid/KangProxy/KangProxy/https/https.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://raw.githubusercontent.com/officialputuid/KangProxy/KangProxy/http/http.txt", # | Fetched: 1 | Good: 0 | Bad: 1 | Success Rate: 0.00%
#"https://github.com/BreakingTechFr/Proxy_Free/blob/main/proxies/http.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/BreakingTechFr/Proxy_Free/blob/main/proxies/socks4.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/BreakingTechFr/Proxy_Free/blob/main/proxies/socks5.txt", # | Fetched: 1 | Good: 0 | Bad: 1 | Success Rate: 0.00%
#"https://github.com/MuRongPIG/Proxy-Master/blob/main/http.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/MuRongPIG/Proxy-Master/blob/main/socks4.txt", # | Fetched: 13 | Good: 0 | Bad: 13 | Success Rate: 0.00%
#"https://github.com/MuRongPIG/Proxy-Master/blob/main/socks5.txt", # | Fetched: 12 | Good: 0 | Bad: 12 | Success Rate: 0.00%
#"https://github.com/ProxyScraper/ProxyScraper/blob/main/http.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/ProxyScraper/ProxyScraper/blob/main/socks4.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/ProxyScraper/ProxyScraper/blob/main/socks5.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/zenjahid/FreeProxy4u/blob/main/http.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://raw.githubusercontent.com/dinoz0rg/proxy-list/main/scraped_proxies/http.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
#"https://github.com/zebbern/Proxy-Scraper/blob/main/proxies.txt", # | Fetched: 660 | Good: 0 | Bad: 500 | Success Rate: 0.00%
#"https://github.com/Vann-Dev/proxy-list/blob/main/proxies/http.txt", # | Fetched: 0 | Good: 0 | Bad: 0 | Success Rate: 0.00%
        ]

        total_fetched_count_overall = 0 # Total proxies fetched from all sources (including duplicates and seen)
        unique_fetched_count_overall = 0 # Total unique new proxies fetched from all sources

        for url in urls:
            retries = 3
            wait_time = 2
            newly_fetched_proxies_count = 0 # Counter for new proxies from this source
            total_fetched_from_source = 0 # Counter for total proxies fetched from this source

            while retries > 0:
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        proxies = self.parse_proxies_from_response(response, url)
                        total_fetched_from_source = len(proxies) # Count total proxies from source response
                        new_proxies_tuples = []
                        for proxy in proxies:
                            if proxy not in self.seen_proxies: # Check against seen_proxies
                                new_proxies_tuples.append((url, proxy)) # Create tuple if new
                                self.seen_proxies.add(proxy) # Add to seen_proxies set
                                newly_fetched_proxies_count += 1 # Increment counter for new proxies

                        self.proxy_list.extend(new_proxies_tuples) # Extend proxy_list with tuples
                        if url not in self.source_stats:
                            self.source_stats[url] = {
                                'total_fetched': 0,
                                'fetched': 0, # Initialize unique_fetched
                                'good': 0,
                                'bad': 0
                            }
                        self.source_stats[url]['fetched'] += newly_fetched_proxies_count # Increment unique fetched
                        total_fetched_count_overall += total_fetched_from_source # Update overall total fetched
                        unique_fetched_count_overall += newly_fetched_proxies_count # Update overall unique fetched

                        self.logger.info(f"From {url}: Fetched {total_fetched_from_source} proxies, {newly_fetched_proxies_count} unique proxies") # Detailed log
                        break
                    else:
                        self.logger.debug(f"Bad response from {url} (status code {response.status_code}), retrying...")
                except Exception as e:
                    self.logger.debug(f"Exception while fetching from {url}: {e}")

                retries -= 1
                if retries > 0:
                    time.sleep(wait_time)
                    wait_time *= 2

            if retries == 0:
                self.logger.warning(f"Failed to fetch proxies from {url} after 3 attempts.")

        self.logger.info(f"Fetch Summary: Total proxies fetched (including duplicates): {total_fetched_count_overall}, Unique new proxies fetched: {unique_fetched_count_overall}")
        self.logger.info(f"Total proxies in proxy_list after fetching: {len(self.proxy_list)}")

    def parse_proxies_from_response(self, response, url):
        if "geonode.com" in url:
            data = response.json()
            return [f"{protocol}://{proxy['ip']}:{proxy['port']}" for proxy in data['data'] for protocol in proxy['protocols']]
        elif "spys.me/proxy.txt" in url:
            lines = response.text.splitlines()
            return [line.split()[0] for line in lines if ":" in line and line[0].isdigit()]
        elif "spys.me/socks.txt" in url:
            lines = response.text.splitlines()
            return [f"socks5://{line.split()[0]}" for line in lines if ":" in line and line[0].isdigit()]
        elif "openproxy.space" in url:
            if "socks4" in url:
                protocol = "socks4"
            elif "socks5" in url:
                protocol = "socks5"
            else:
                protocol = "http"

            soup = BeautifulSoup(response.text, 'html.parser')
            script_tag = soup.find("script", text=lambda text: text and "window.__NUXT__" in text)
            if script_tag and script_tag.string:
                matches = re.findall(r'items:\[(.*?)\]', script_tag.string, re.DOTALL)
                proxies = []
                for match in matches:
                    for proxy in match.split(","):
                        cleaned_proxy = proxy.replace('"', '')
                        proxies.append(f"{protocol}://{cleaned_proxy}")
                return proxies
        elif "proxy-list.download" in url or "TheSpeedX/PROXY-List" in url or "zloi-user/hideip.me" in url:
            url_path = url.split("://", 1)[-1]
            
            if "socks4" in url_path:
                protocol = "socks4"
            elif "socks5" in url_path:
                protocol = "socks5"
            elif "http.txt" in url_path:
                protocol = "http"
            elif "https.txt" in url_path:
                protocol = "https"
            else:
                protocol = "http" # Default for these sources if not specified
            
            proxies = re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}:\d{2,5}\b', response.text)
            return [f"{protocol}://{proxy}" for proxy in proxies]

        # Fallback for other plain text formats
        proxies = re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}:\d{2,5}\b', response.text)
        return [f"http://{proxy}" for proxy in proxies]

    def validate_proxy(self, proxy_tuple): # takes tuple as input
        source_url, proxy = proxy_tuple
        protocol = proxy.split("://")[0] if "://" in proxy else "http"
        proxy_address = proxy.split("://")[1] if "://" in proxy else proxy
        ip, port = proxy_address.split(":")
        port = int(port)
        try:
            with socket.create_connection((ip, port), timeout=0.5):
                pass

            proxies = {protocol: proxy}
            response = requests.head("http://httpbin.org/ip", proxies=proxies, timeout=1.5, verify=False)

            if response.status_code == 200:
                return True
            else:
                with self.bad_proxies_lock:
                    self.bad_proxies.add(proxy)
                return False
        except (socket.timeout, socket.gaierror, ConnectionRefusedError, OSError, ValueError, requests.RequestException):
            with self.bad_proxies_lock:
                self.bad_proxies.add(proxy)
            return False


if __name__ == "__main__":
    proxy_handler_debug = ProxyHandler(log_level=logging.DEBUG, log_file='proxy_debug.log')
    proxy_handler_debug.check_all_proxies()