import requests
import time
from bs4 import BeautifulSoup
import subprocess
import re
import math
from unidecode import unidecode
import os
import sys
import traceback
from queries import *
from resolveFanslyProfiles import *
import concurrent.futures
import threading
from datetime import datetime, timedelta
import json
import mimetypes
mimetypes.add_type('video/x-m4v', '.m4v')
mimetypes.add_type('video/x-flv', '.flv')
mimetypes.add_type('video/x-ms-wmv', '.wmv')
mimetypes.add_type('video/3gpp', '.3gp')
mimetypes.add_type('video/ogg', '.ogg')
mimetypes.add_type('video/x-matroska', '.mkv')
mimetypes.add_type('video/mp2t', '.ts')
mimetypes.add_type('video/mp4', '.mp4v')
mimetypes.add_type('video/x-xvid', '.xvid')
mimetypes.add_type('video/x-divx', '.divx')

import logging
import socket
import random
from requests.exceptions import HTT<PERSON>Error, Timeout
from queue import Queue
from urllib.parse import urlencode, quote
from ProxyHandler import *
import signal
import warnings
import urllib3.connectionpool
from queue import Empty
import platform
from WebDriverPool import WebDriverPool
from logger_helper import setup_logger
import ndjson_helper
from line_profiler import profile

# Suppress Selenium's internal debug logs
logging.getLogger('selenium').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)

# Disable all warnings in urllib3.connectionpool
urllib3.connectionpool.log.warning = lambda *args, **kwargs: None

# Get the directory of the current script
script_dir = os.path.dirname(os.path.abspath(__file__))

# Change the working directory to the script's location
os.chdir(script_dir)

print(f"Current working directory: {os.getcwd()}")

MAX_RETRIES = 10
INITIAL_WAIT = 30
WAIT_INCREMENT = 5
INITIAL_DELAY = 5
DELAY_INCREMENT = 5

# Initialize logger using logger_helper
logger = setup_logger(__name__, log_file='getCoomerLog.txt', level=logging.DEBUG)

# Assuming the scraper instance is created as `scraper`
def signal_handler(sig, frame):
    logger.info("Stop signal received. Setting stop_requested to True.")
    scraper.stop_requested = True

# Register the signal handler
signal.signal(signal.SIGINT, signal_handler)


class ScraperWorker:
    def __init__(self, scraper, query, new_links_file=None, username=None, parse_entries=True):
        self.scraper = scraper
        self.query = query
        self.username = username
        self.parse_entries = parse_entries
        self.new_links_file = new_links_file
        self.logger = logging.getLogger(__name__)

    @profile
    def handle_failed_query_request(self, query, offset, retry_count):
        if retry_count < self.scraper.max_retries:
            with self.scraper.query_lock:
                self.scraper.query_queue.put((query, offset, retry_count + 1))
                self.logger.info(f"Query {query} re-added to queue with retry count {retry_count + 1}")
        else:
            self.logger.info(f"Query {query} didn't yield anything, aborting.")
            self.scraper.searchQueries.pop(query, None)

    @profile
    def handle_failed_post_request(self, url, offset, retry_count):
        if retry_count < self.scraper.max_retries:
            with self.scraper.query_lock:
                # Queue the original profile URL (self.query) and the failed offset
                self.scraper.query_queue.put((self.username, self.query, offset, retry_count + 1))
                self.logger.info(f"Query {self.username} re-added to queue with retry count {retry_count + 1}")
        else:
            self.logger.info(f"Query {self.username} didn't yield anything, aborting.")
            # This pop seems incorrect, should pop the original query/url
            # self.scraper.searchQueries.pop(url, None)

    @profile
    def is_above_min_size(self, video_url):
        try:
            response = self.scraper.make_request(video_url, method="HEAD")
            if response is not None and response.status_code == 200 and 'Content-Length' in response.headers:
                file_size_mb = int(response.headers['Content-Length']) / (1024 * 1024)
                return file_size_mb >= 50
            return True
        except Exception as e:
            self.logger.error(f"Error checking size for URL {video_url}: {e}")
            return True

    def _build_api_url(self, query_or_api_url, offset, is_query):
        if is_query:
            return f"https://coomer.st/api/v1/posts?q={quote(query_or_api_url, safe='+')}&o={offset}"
        else:
            base_url = f"{query_or_api_url}"
            if "/api/v1/" not in base_url:
                base_url = base_url.replace("https://coomer.st/", "https://coomer.st/api/v1/")
            # For user profile URLs, ensure they end with /posts
            if "/user/" in base_url and not base_url.endswith("/posts"):
                base_url = f"{base_url}/posts"
            if offset > 0:
                return f"{base_url}?o={offset}"
            self.logger.info(f"Getting base url {base_url}")
            return base_url
    @profile
    def get_entries(self, query_or_api_url, offset, retry_count, is_query=True):

        if self.scraper.stop_requested:
            self.logger.info(f"Stop signal detected in get_entries for {query_or_api_url}. Returning None immediately.")
            return None

        all_links = []
        entry_video_pairs = []
        encoded_query_or_api = self._build_api_url(query_or_api_url, offset, is_query)
        response = self.scraper.make_request(encoded_query_or_api, abort_if_timeout=True)
        if response is None:
            if is_query:
                self.handle_failed_query_request(query_or_api_url, offset, retry_count)
            else:
                self.handle_failed_post_request(query_or_api_url, offset, retry_count)
            return None

        try:
            posts = response.json()
        except ValueError:
            print(f"Failed to decode JSON from {encoded_query_or_api}")
            if is_query:
                self.handle_failed_query_request(query_or_api_url, offset, retry_count)
            else:
                self.handle_failed_post_request(query_or_api_url, offset, retry_count)
            return None

        if posts and "posts" in posts:
            posts = posts["posts"]

        if posts:
            for post in posts:
                link = f"https://coomer.st/{post['service']}/user/{post['user']}/post/{post['id']}"

                with self.scraper.crawled_links_lock:
                    if link in self.scraper.crawled_links:
                        continue
                all_links.append(link)

                video_urls, content, has_incomplete_rewards = self.scraper._extract_video_urls_and_content_from_api_post(post)
                if video_urls and has_incomplete_rewards is False:
                    entry_video_pairs.append((link, video_urls, content))

        if len(all_links) <= 0 and len(posts) <= 0:
            print("No links found: " + str(posts) + " - " + response.url)
            return None

        for link, videos, content in entry_video_pairs:
            if not videos:
                with self.scraper.file_lock:
                    self.scraper.crawled_links.add(link)
                    
        return entry_video_pairs
        
    def get_links(self, query_or_api_url, offset, retry_count, is_query=True):
        encoded_query_or_api = self._build_api_url(query_or_api_url, offset, is_query)
        response = self.scraper.make_request(encoded_query_or_api, abort_if_timeout=True)
        if response is None:
            if is_query:
                self.handle_failed_query_request(query_or_api_url, offset, retry_count)
            else:
                self.handle_failed_post_request(query_or_api_url, offset, retry_count)
            return None

        try:
            posts = response.json()
        except ValueError:
            print(f"Failed to decode JSON from {encoded_query_or_api}")
            if is_query:
                self.handle_failed_query_request(query_or_api_url, offset, retry_count)
            else:
                self.handle_failed_post_request(query_or_api_url, offset, retry_count)
            return None

        if posts and "posts" in posts:
            posts = posts["posts"]
            
        all_links = []
        
        if posts:
            for post in posts:
                link = f"https://coomer.st/{post['service']}/user/{post['user']}/post/{post['id']}"
                all_links.append(link)

        return all_links
        
    @profile
    def start_scrape_status(self):
        with self.scraper.status_lock:
            self.scraper.processing_queries += 1

        self.logger.info("Scraping page: " + self.query)

    @profile
    def scrape_posts(self, offset, retry_count):
        self.start_scrape_status()
        # self.query is the original profile URL
        initial_api_url = self.query.replace("https://coomer.st", "https://coomer.st/api/v1")

        try:
            # Pass the constructed URL and the correct offset to the worker
            self.scrape_posts_worker(initial_api_url, offset, retry_count)
            self.register_username_scrapped(self.username)

        except Exception as e:
            self.logger.info("Error scrape_posts:\n" + traceback.format_exc())
            self.logger.info("ABORTED scrape_posts_worker for " + self.query + " due to " + str(e))

    @profile
    def scrape_posts_worker(self, apiUrl, offset, retry_count):
        query = self.query
        if offset == 0 and retry_count == 0:
            self.start_scrape_status()

        consecutive_visited_pages_count = 0 # Track consecutive fully visited pages for this query
        try:
            while True:
                if self.scraper.stop_requested:
                    self.logger.info(f"Stop signal detected in scrape_posts_worker for {query}. Finishing current page and stopping.")
                    break

                entries = self.get_entries(apiUrl, offset, retry_count, is_query=False)
                if entries is None:
                    self.finish_scrape_status()
                    return

                any_processed = False # Track if any *new* link was processed on this page
                if len(entries) > 0:
                    self.logger.info(f"{apiUrl} - found entries: {len(entries)}, offset: {offset}, found links: {self.scraper.found_links_count}, processed queries {self.scraper.processed_queries}")

                    if self.parse_entries:
											 
                        for cur_entry, (link, video_urls, content) in enumerate(entries, start=1):
                            self.logger.info(f"{query} - Processing entry {cur_entry}/{len(entries)}, offset: {offset}, found links: {self.scraper.found_links_count}, processed queries {self.scraper.processed_queries}")

                            processed = self.process_link(link, content, video_urls)
                            if processed:
                                any_processed = True # A new link was found and processed

                # Check for abort condition based on consecutive visited pages
                if self.scraper.abortEarlyIfVisitedPageFully:
                    if not any_processed:
                        consecutive_visited_pages_count += 1
                        self.logger.info(f"{query} - Page fully visited (no new links processed). Consecutive count: {consecutive_visited_pages_count}")
                    else:
                        # Reset counter if any link was processed on this page
                        if consecutive_visited_pages_count > 0:
                             self.logger.info(f"{query} - New links found on this page. Resetting consecutive visited page count.")
                        consecutive_visited_pages_count = 0

                    if consecutive_visited_pages_count >= 6:
                        self.logger.info(f"{query} has been visited fully for {consecutive_visited_pages_count} consecutive pages. Aborting further pages for this query.")
                        self.finish_scrape_status()
                        return

                offset += 50
                self.logger.info(f"{query} - Processing offset: {offset}, found links: {self.scraper.found_links_count}, processed queries {self.scraper.processed_queries}")
                with self.scraper.query_lock:
                    self.scraper.searchQueries[query] = offset
        finally:
            self.finish_scrape_status()


    @profile
    def scrape_queries(self, offset, retry_count):
        try:
            self.scrape_queries_worker(self.query, offset, retry_count)
            self.register_username_scrapped(self.username)
            self.scraper.finish_query_processing(self.query)
        except Exception as e:
            self.logger.info("Error scrape_queries:\n" + traceback.format_exc())
            self.logger.info("ABORTED scrape_queries_worker for " + self.query + " due to " + str(e))

    @profile
    def scrape_queries_worker(self, apiUrl, offset, retry_count):
        consecutive_visited_pages_count = 0 # Track consecutive fully visited pages for this query
        try:
            self.logger.info(f"Starting scrape_queries_worker for {apiUrl} with offset: {offset} and retry_count: {retry_count}")
            query = self.query
            if offset == 0 and retry_count == 0:
                try:
                    self.start_scrape_status()
                except Exception as e:
                    self.logger.error(f"{query} - Error initializing scrape status: {e}", exc_info=True)

            try:
                while True:
                    if self.scraper.stop_requested:
                        self.logger.info(f"Stop signal detected in scrape_queries_worker for {query}. Finishing current page and stopping.")
                        break

                    try:
                        entries = self.get_entries(apiUrl, offset, retry_count, is_query=True) # Pass is_query=True
                    except Exception as e:
                        self.logger.error(f"{query} - Error fetching post entries: {e}", exc_info=True)
                        entries = None

                    if entries is None:
                        try:
                            self.logger.info(f"{query} - No more entries found or error occurred. Finishing scrape status.")
                            self.finish_scrape_status()
                        except Exception as e:
                            self.logger.error(f"{query} - Error finishing scrape status: {e}", exc_info=True)
                        return

                    any_processed = False # Track if any *new* link was processed on this page
                    if len(entries) > 0:
                        try:
                            self.logger.info(f"{apiUrl} - Found {len(entries)} entries. Offset: {offset}, Found links: {self.scraper.found_links_count}, Processed queries: {self.scraper.processed_queries}")
                            if self.parse_entries:
													 
                                for cur_entry, (link, video_urls, content) in enumerate(entries, start=1):
                                    try:
                                        self.logger.info(f"{query} - Processing entry {cur_entry}/{len(entries)}, Offset: {offset}, Found links: {self.scraper.found_links_count}, Processed queries: {self.scraper.processed_queries}")
                                        processed = self.process_link(link, content, video_urls)
                                        self.logger.info(f"{query} - Done processing entry {cur_entry}/{len(entries)}, Offset: {offset}, Found links: {self.scraper.found_links_count}, Processed queries: {self.scraper.processed_queries}")
                                        if processed:
                                            any_processed = True # A new link was found and processed

                                    except Exception as e:
                                        self.logger.error(f"{query} - Error processing link {link}: {e}", exc_info=True)

                                # Check for abort condition based on consecutive visited pages
                                if self.scraper.abortEarlyIfVisitedPageFully:
                                    if not any_processed:
                                        consecutive_visited_pages_count += 1
                                        self.logger.info(f"{query} - Page fully visited (no new links processed). Consecutive count: {consecutive_visited_pages_count}")
                                    else:
                                         # Reset counter if any link was processed on this page
                                         if consecutive_visited_pages_count > 0:
                                             self.logger.info(f"{query} - New links found on this page. Resetting consecutive visited page count.")
                                         consecutive_visited_pages_count = 0

                                    if consecutive_visited_pages_count >= 3:
                                        self.logger.info(f"{query} has been visited fully for {consecutive_visited_pages_count} consecutive pages. Aborting further pages for this query.")
                                        try:
                                            self.finish_scrape_status()
                                        except Exception as e:
                                            self.logger.error(f"{query} - Error finishing scrape status during early abort: {e}", exc_info=True)
                                        return
                        except Exception as e:
                            self.logger.error(f"{query} - Error parsing entries: {e}", exc_info=True)

                    try:
                        offset += 50
                        with self.scraper.query_lock:
                            self.scraper.searchQueries[query] = offset
                    except Exception as e:
                        self.logger.error(f"{query} - Error updating offset: {e}", exc_info=True)
            finally:
                 # Ensure finish_scrape_status is called even if an exception occurs mid-loop, unless already called by return
                 # Check if the query is still considered processing before decrementing
                 with self.scraper.status_lock:
                     if self.query in self.scraper.processed_queries_list: # Already finished via return
                         pass
                     else:
                         try:
                             self.logger.info(f"{query} - Finishing scrape status in finally block.")
                             self.finish_scrape_status()
                         except Exception as e:
                             self.logger.error(f"{query} - Error finishing scrape status in finally block: {e}", exc_info=True)
        except Exception as e:
            self.logger.error(f"Unexpected error in scrape_queries_worker: {e}", exc_info=True)
            # Ensure status is updated even with unexpected errors before the try block
            with self.scraper.status_lock:
                 if self.query not in self.scraper.processed_queries_list:
                     self.finish_scrape_status()

    @profile
    def register_username_scrapped(self, username):
        if username:
            with self.scraper.cache_lock:
                if 'scraped_urls' not in self.scraper.url_cache[username]:
                    self.scraper.url_cache[username]['scraped_urls'] = []
                scraped_urls = self.scraper.url_cache[username]['scraped_urls']
                existing_entry = next((entry for entry in scraped_urls if entry['url'] == self.query), None)
                if existing_entry:
                    existing_entry['date_scraped'] = datetime.now().isoformat()
                else:
                    scraped_urls.append({
                        'url': self.query,
                        'date_scraped': datetime.now().isoformat()
                    })
            ndjson_helper.write(self.scraper.url_cache_file, self.scraper.url_cache)

    @profile
    def finish_scrape_status(self):
        with self.scraper.status_lock:
            self.scraper.processing_queries -= 1
            self.scraper.processed_queries += 1
            self.scraper.processed_queries_list.append(self.query)

        self.logger.info(f"{self.query} Finished scraping")

    @profile
    def process_link(self, link, content, video_urls):
        self.logger.info(f"Processing link: {link}")
        should_parse = self.scraper._check_and_add_link(link)

        if should_parse:
            try:
                self.parse_link(link, content, video_urls)
            except Exception as e:
                self.logger.error(f"Caught exception parsing {link} - {e}")
        return should_parse

    @profile
    def get_soup_with_video_links(self, link):
        page_source = self.scraper.webdriver_pool.get_source(link, selector="footer.post__footer", scroll=False)
        if page_source is None:
            self.logger.info(f"{link} - No video URLs found (page_source is None).")
            return None, []

        soup = BeautifulSoup(page_source, 'html.parser')
        attachments = soup.find('ul', class_='post__attachments')
        video_urls = []

        if attachments:
            for li in attachments.find_all('li', class_='post__attachment'):
                attachment_link = li.find('a', class_='post__attachment-link')
                if attachment_link:
                    attachment_href = attachment_link['href']
                    if self.scraper.is_video_url_by_extension(attachment_href):
                        video_urls.append(attachment_href)

        source_tag = soup.find('source', src=True)
        if source_tag:
            video_urls.append(source_tag['src'])

        if video_urls:
            video_urls = list(dict.fromkeys([url.split('?')[0] for url in video_urls]))
            self.logger.info(f"{link} - Found {len(video_urls)} video URLs: {video_urls}")
            return soup, video_urls

        self.logger.info(f"{link} - No video URLs found.")
        return None, []

    @profile
    def get_description(self, link, profile, soup):
        post_warning = soup.find('div', class_='post__warning', style='color: red;')
        if post_warning:
            pre_tag = post_warning.find('pre')
            if pre_tag and 'This post is missing paid rewards' in pre_tag.text:
                self.logger.info(f"{link} - Missing paid rewards detected.")
                return None

        description = ""
        h2_content = soup.find('h2', text='Content')
        post_content = soup.find('div', class_='post__content')
        if h2_content and post_content:
            pre_tag = post_content.find('pre')
            if pre_tag:
                description = pre_tag.get_text(strip=True)
        return description

    @profile
    def parse_link(self, link, content, video_urls):
        profile_match = re.search(r'([^/]+)/post/', link)
        if not profile_match:
            self.logger.warning(f"Invalid link format: {link}")
            return

        profile = profile_match.group(1)
        with self.scraper.profiles_lock:
            if profile in self.scraper.profiles_data and self.scraper.profiles_data[profile]["unwanted"]:
                return

        try:
            self.print_link(link, content, video_urls)
        except Exception as e:
            self.logger.info(f"Caught exception parsing {link} - {e}")

    @profile
    def print_link(self, link, content, video_urls):
        self.logger.info(f"FOUND link: {link}")
        if self.new_links_file is not None:
            with self.scraper.file_lock:
                new_entry = {
                    "url": link,
                    "query": self.query,
                    "description": content,
                    "video_urls": video_urls
                }
                ndjson_helper.append(self.new_links_file.name, new_entry)
                with self.scraper.status_lock:
                    self.scraper.found_links_count += 1

class WebsiteScraper:
    BROWSER_HEADERS = {
        'accept': 'text/css,*/*;q=0.1',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-arch': '"x86"',
        'sec-ch-ua-bitness': '"64"',
        'sec-ch-ua-full-version': '"139.0.7258.139"',
        'sec-ch-ua-full-version-list': '"Not;A=Brand";v="********", "Google Chrome";v="139.0.7258.139", "Chromium";v="139.0.7258.139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-model': '""',
        'sec-ch-ua-platform': '"Windows"',
        'sec-ch-ua-platform-version': '"10.0.0"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'content-type': 'text/plain'
    }

    @profile
    def __init__(self, crawled_links, new_links_path, webdriver_pool, proxy_handler, timeout_threshold=60, abortEarlyIfVisitedPageFully=False):
        self.logger = logging.getLogger(__name__)
        self.base_url = "https://coomer.st"
        self.proxy_handler = proxy_handler
        self.crawled_links = crawled_links
        self.new_links_path = new_links_path
        self.timeout_threshold = timeout_threshold
        self.abortEarlyIfVisitedPageFully = abortEarlyIfVisitedPageFully
        self.webdriver_pool = webdriver_pool


        self.file_lock = threading.Lock()
        self.status_lock = threading.Lock()
        self.crawled_links_lock = threading.Lock()
        self.cache_lock = threading.Lock()

        self.base_onlyfans_url = "https://coomer.st/onlyfans/user/"
        self.base_fansly_url = "https://coomer.st/fansly/user/"
        self.url_cache = ndjson_helper.read('url_cache.json')
        self.query_cache = ndjson_helper.read('query_cache.json')


        self.processed_queries = 0
        self.processing_queries = 0
        self.total_queries = 0
        self.processed_queries_list = []

        self.crawled_links = load_links("coomerlinksHistory.txt")
        self.file_lock = threading.Lock()
        self.crawled_links_lock = threading.Lock()
        self.query_lock = threading.Lock()

        self.searchQueries = {}
        self.max_retries = 10
        self.query_queue = Queue()

        self.url_cache_file = 'url_cache.json'
        self.query_cache_file = 'query_cache.json'
        self.profiles_data = ndjson_helper.read("profiles.json")
        self.profiles_lock = threading.Lock()

        self.found_links_count = 0
        self.stop_requested = False

    @profile
    def _check_and_add_link(self, link):
        with self.crawled_links_lock:
            if link not in self.crawled_links:
                self.crawled_links.add(link)
                should_parse = True
            else:
                should_parse = False

        return should_parse

    @profile
    def is_video_url_by_extension(self, url):
        mime_type, _ = mimetypes.guess_type(url)
        if mime_type and mime_type.startswith('video/'):
            return True
        else:
            return False

    @profile
    def _extract_video_urls_and_content_from_api_post(self, post_data):
        video_urls = set()
        content = None
        has_incomplete_rewards = False

        if "incomplete_rewards" in post_data:
            has_incomplete_rewards = True

        if "file" in post_data and "path" in post_data["file"]:
            candidate_link = post_data["file"]["path"]
            if not candidate_link.startswith("http"):
                candidate_link = "https://n1.coomer.st/data" + candidate_link

            if self.is_video_url_by_extension(candidate_link):
                video_urls.add(candidate_link)

        if "attachments" in post_data:
            for attachment in post_data["attachments"]:
                if "path" in attachment:
                    candidate_link = attachment["path"]
                    if not candidate_link.startswith("http"):
                        candidate_link = "https://n1.coomer.st/data" + candidate_link

                    if self.is_video_url_by_extension(candidate_link):
                        video_urls.add(candidate_link)

        if video_urls and has_incomplete_rewards is False:
            if "content" in post_data:
                content = post_data["content"]
                if "post" in post_data:
                        if "incomplete_rewards" in post_data["post"]:
                            has_incomplete_rewards = True                                     
            else:
                try:
                    service = post_data.get("service")
                    creator_id = post_data.get("user")
                    post_id = post_data.get("id")

                    if service and creator_id and post_id:
                        api_link = f"https://coomer.st/api/v1/{service}/user/{creator_id}/post/{post_id}"
                        try:
                            response = self.make_request(api_link, log_request = False)
                            if response is not None:
                                api_data = response.json()
                                if "post" in api_data:
                                    if "content" in api_data["post"]:
                                        content = api_data["post"]["content"]
                                        
                                    if "incomplete_rewards" in api_data["post"]:
                                        has_incomplete_rewards = True
                                        
                                elif "post" in api_data:
                                    print(f"Warning: 'content' field not found within 'post' in API response from {api_link}")
                                else:
                                    print(f"Warning: 'post' field not found in API response from {api_link}")

                        except requests.exceptions.RequestException as e:
                            print(f"Error fetching content from API {api_link}: {e}")
                    else:
                        print("Warning: Could not construct API link to fetch content. Missing service, user, or id in post_data.")
                except Exception as e:
                    print(f"2 Error fetching content from API {api_link}: {e}")
        if content is None:
            content = ""

        return list(dict.fromkeys([url.split('?')[0] for url in video_urls])), content, has_incomplete_rewards

    @profile
    def parse_queries(self):
        sorted_queries = get_sorted_queries()
        sorted_queries = [x for x in sorted_queries if x not in ignored_queries]
        self.process_queries(sorted_queries)

    @profile
    def process_queries(self, queriesToProcess):
        self.total_queries = len(queriesToProcess)
        self.logger.info(f"Starting to process {self.total_queries} queries.")
        for query in queriesToProcess:
            if self.should_process_query(query):
                self.query_queue.put((query, 0, 0))
            else:
                self.logger.info(f"Skipping query {query} as it was processed recently.")

        with open(self.new_links_path, 'a+', encoding='utf-8') as new_links_file:
            with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_THREADS) as executor:
                futures = []

                while not self.query_queue.empty() or any(f.running() for f in futures):
                    if self.stop_requested:
                        self.logger.info("Stop requested. Halting further processing.")
                        break


                    while len(futures) < 5 and not self.query_queue.empty() and not self.stop_requested:
                        query, offset, retry_count = self.query_queue.get()
                        worker = ScraperWorker(self, query, new_links_file)
                        future = executor.submit(worker.scrape_queries, offset, retry_count)
                        futures.append(future)


                    completed_futures = [f for f in futures if f.done()]
                    for f in completed_futures:
                        futures.remove(f)
                        try:
                            result = f.result()
                        except Exception as e:
                            self.logger.error(f"Exception occurred in thread: {e}", exc_info=True)


                    time.sleep(0.1)

                concurrent.futures.wait(futures)

        self.logger.info("Fully processed queries.")

    @profile
    def parse_profiles(self):
        include_invalid = False
        profiles_to_resolve = [
            username for username in profiles
            if username not in self.url_cache or (not self.url_cache[username].get('has_valid_urls') and include_invalid)
        ]
        profiles_to_resolve = [username for username in profiles_to_resolve if username not in profiles_to_ignore]

        print(f"parse_profiles: {profiles_to_resolve}")
        if profiles_to_resolve:
            self.logger.info(f"Resolving {len(profiles_to_resolve)} profiles.")

            with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_THREADS) as executor:
                futures = {
                    executor.submit(self.resolve_and_validate_urls, username): username
                    for username in profiles_to_resolve
                    if not self.stop_requested
                }

                for future in concurrent.futures.as_completed(futures):
                    username = futures[future]
                    try:
                        future.result()
                        remaining = len(futures) - len([f for f in futures if f.done()])
                        self.logger.info(f"Resolved {username}. Remaining: {remaining}")
                    except Exception as e:
                        self.logger.error(f"Error resolving profile {username}: {e}")

                    if self.stop_requested:
                        self.logger.info("Stop requested. Halting profile resolution.")
                        break

        try:
            self.total_queries = 0
            self.logger.info("Starting to process profiles.")

            for username, data in self.url_cache.items():
                if self.stop_requested:
                    self.logger.info("Stop requested. Halting further profile processing.")
                    break

                if username in profiles:
                    for url in data.get("urls", []):
                        if self.stop_requested:
                            self.logger.info("Stop requested. Halting further URL submissions.")
                            break

                        if self.should_process(username, url):
                            self.query_queue.put((username, url, 0, 0))
                            self.total_queries += 1

            self.logger.info(f"Total queries to process: {self.total_queries}")

            with open(self.new_links_path, 'a+', encoding='utf-8') as new_links_file:
                with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_THREADS) as executor:
                    futures = []

                    while not self.query_queue.empty() or any(f.running() for f in futures):
                        if self.stop_requested:
                            self.logger.info("Stop requested. Halting further processing.")
                            break

                        while len(futures) < MAX_CONCURRENT_THREADS and not self.query_queue.empty() and not self.stop_requested:
                            username, url, offset, retry_count = self.query_queue.get()
                            worker = ScraperWorker(self, url, new_links_file, username=username)
                            future = executor.submit(worker.scrape_posts, offset, retry_count)
                            futures.append(future)

                        completed_futures = [f for f in futures if f.done()]
                        for f in completed_futures:
                            futures.remove(f)
                            try:
                                result = f.result()
                            except Exception as e:
                                self.logger.error(f"Exception occurred in thread: {e}", exc_info=True)

                        time.sleep(0.1)

                    concurrent.futures.wait(futures)

            self.logger.info("Finished processing profiles.")

        except Exception as e:
            self.logger.error(f"Error during URL processing: {e}")

        if not self.stop_requested:
            queries_to_process = list(profiles)
            self.process_queries(queries_to_process)

        print("parse_profiles is finished processing")

    @profile
    def resolve_and_validate_urls(self, username):
        urls = []
        thread_name = threading.current_thread().name

        if username.isdigit():
            urls.append(f"{self.base_fansly_url}{username}")
        else:
            urls.append(f"{self.base_onlyfans_url}{username}")
            self.logger.info(f"Attempting to resolve fansly ID for {username}")
            numeric_id = get_fansly_numeric_id(username)
            self.logger.info(f"Resolved fansly ID for {username}")
            if numeric_id and str(numeric_id).isdigit():
                urls.append(f"{self.base_fansly_url}{numeric_id}")

        valid_urls = [url for url in urls if self.is_valid_url(url)]
        if not valid_urls:
            self.update_cache_entry(username, valid_urls, False)
            self.logger.info(f"No valid URLs found for {username}. Marked as invalid.")
        else:
            self.update_cache_entry(username, valid_urls, True)
        return valid_urls

    @profile
    def is_valid_url(self, url):
        try:
            response = requests.head(url, timeout=5, allow_redirects=True)
            return response.status_code != 404 and response.url != "https://coomer.st/artists"
        except requests.RequestException:
            return False

    @profile
    def update_cache_entry(self, username, urls, has_valid_urls):
        with self.cache_lock:
            if username not in self.url_cache:
                self.url_cache[username] = {
                    'urls': urls,
                    'has_valid_urls': has_valid_urls,
                    'scraped_urls': []
                }
            else:
                self.url_cache[username]['urls'] = urls
                self.url_cache[username]['has_valid_urls'] = has_valid_urls

        ndjson_helper.write(self.url_cache_file, self.url_cache)

    @profile
    def finish_query_processing(self, query):
        with self.cache_lock:
            self.query_cache[query] = {
                "status": "completed",
                "last_processed": datetime.now().isoformat()
            }
        ndjson_helper.write(self.query_cache_file, self.query_cache)

    @profile
    def update_query_cache(self, query, data):
        with self.cache_lock:
            self.query_cache[query] = data
        ndjson_helper.write(self.query_cache_file, self.query_cache)

    @profile
    def should_process_query(self, query):
        with self.cache_lock:
            if query in self.query_cache:
                last_processed = self.query_cache[query].get('last_processed')
                if last_processed:
                    date_processed = datetime.fromisoformat(last_processed)
                    if datetime.now() - date_processed < timedelta(weeks=1):
                        return False
            return True

    @profile
    def should_process(self, username, url):
        with self.cache_lock:
            scraped_urls = self.url_cache[username].get('scraped_urls', [])
            for entry in scraped_urls:
                if entry['url'] == url:
                    date_scraped = datetime.fromisoformat(entry['date_scraped'])
                    if datetime.now() - date_scraped < timedelta(weeks=1):
                        return False
        return True


    def extract_user_from_url(self, url):
        if '/user/' in url:
            return url.split('/user/')[1].split('/')[0]
        return url

    @profile
    def parse_queries_with_names(self, query, offset, retry_count, new_links_file):
        worker = ScraperWorker(self, query, new_links_file)
        post_delay = 10
        consecutive_visited_pages_count = 0 # Track consecutive fully visited pages for this query
        while True:
            try:
                if self.stop_requested:
                    self.logger.info(f"Stop signal detected in parse_queries_with_names for {query}. Stopping.")
                    break
    
                entries = worker.get_entries(query, offset, retry_count)
                if entries is None:
                    # If get_entries returns None, it means the query failed or no more posts.
                    # We should not consider this a "fully visited page" in the same way as an empty but successful page.
                    # However, if it's consistently failing, the retry mechanism in get_entries should handle it.
                    # For now, let's assume this means the end for this query.
                    self.logger.info(f"{query} - No more entries or error in get_entries. Aborting.")
                    return

                any_processed = False # Track if any *new* link was processed on this page
                if len(entries) > 0:
                    for cur_entry, (link, video_urls, content) in enumerate(entries, start=1):
                        try:
                            processed_this_link = False
                            if content:
                                tokens = re.findall(r'@[\w.-]+', content.lower())
                                tokens = [re.sub(r"'[^ ]*", "", token).strip("@").rstrip(".") for token in tokens]
                                tokens = list(set(tokens))
                                username = self.extract_user_from_url(link)
                                tokens = [token for token in tokens if token != username.lower()]
                                if len(tokens) >= 2:
                                    # Assuming print_link implies a "new" link was found and processed for this specific logic
                                    # We need a way to know if print_link actually added a *new* link.
                                    # For simplicity, let's assume if it reaches here and prints, it's "processed".
                                    # A more robust way would be for print_link to return a status.
                                    with self.crawled_links_lock: # Check if link is new before printing
                                        if link not in self.crawled_links:
                                            processed_this_link = True
                                    worker.print_link(link, content, video_urls) # print_link adds to crawled_links
                            else:
                                print(f"no content found in {link}")
        
                            if processed_this_link:
                                any_processed = True

                            self.logger.info(f"{query} - Processing entry {cur_entry}/{len(entries)}, Offset: {offset}, Found links: {self.found_links_count}, Processed queries: {self.processed_queries}")
                        except Exception as e:
                            print(f"Post URL error: {e}. Retrying in {post_delay} seconds...")
                            time.sleep(post_delay)
                            post_delay = min(post_delay * 2, 60)
                
                # Check for abort condition based on consecutive visited pages
                if self.abortEarlyIfVisitedPageFully:
                    if not any_processed and len(entries) > 0: # Only count if the page had entries but none were new
                        consecutive_visited_pages_count += 1
                        self.logger.info(f"{query} - Page fully visited (no new links processed). Consecutive count: {consecutive_visited_pages_count}")
                    elif any_processed: # Reset counter if any link was processed on this page
                        if consecutive_visited_pages_count > 0:
                             self.logger.info(f"{query} - New links found on this page. Resetting consecutive visited page count.")
                        consecutive_visited_pages_count = 0
                    # If len(entries) == 0, it means no posts on this page, which is handled by get_entries returning None eventually or by the loop ending.
                    # We don't increment consecutive_visited_pages_count here as it's not a "visited page with no new links" but rather an "empty page".

                    if consecutive_visited_pages_count >= 3:
                        self.logger.info(f"{query} has been visited fully for {consecutive_visited_pages_count} consecutive pages. Aborting further pages for this query.")
                        return # Abort for this query

            except Exception as e:
                self.logger.error(f"{query} - failed to parse: " + str(e))
                # Potentially add a return or break here if the error is critical for this query
                return # Exit if there's a major error in the loop logic itself

            offset += 50
            with self.query_lock:
                self.searchQueries[query] = offset

    @profile
    def parse_api_posts(self):
        self.searchQueries = dict.fromkeys(blowjobWordsSuffices, 0)
        for query, offset in self.searchQueries.items():
            if self.should_process_query(query):
                self.query_queue.put((query, offset, 0))

        self.crawled_links.update(load_links(r'coomerlinksAPIHistory.txt'))
        with open(self.new_links_path, 'a+', encoding='utf-8') as new_links_file:
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = []

                while not self.query_queue.empty() or any(f.running() for f in futures):
                    while len(futures) < 5 and not self.query_queue.empty():
                        query, offset, retry_count = self.query_queue.get()
                        futures.append(
                            executor.submit(self.parse_queries_with_names, query, offset, retry_count, new_links_file)
                        )
                        time.sleep(1)

                    futures = [f for f in futures if not f.done()]

                concurrent.futures.wait(futures)

    @profile
    def make_request(self, url, abort_if_timeout=False, log_request=True, method="GET"):
        try:
            # Start with a copy of the base browser headers
            headers = self.BROWSER_HEADERS.copy()

            # Dynamically set the Referer header based on the URL structure
            if '/user/' in url:
                try:
                    # Extracts the part of the URL like 'onlyfans/user/username' to use as a referer
                    referer_path = '/'.join(url.split('/')[3:7])
                    headers['Referer'] = f'https://coomer.st/{referer_path}'
                except IndexError:
                    # If the URL format is unexpected, fall back to the base referer
                    headers['Referer'] = 'https://coomer.st/'
            
            # Pass the constructed headers to the proxy handler
            return self.proxy_handler.get_with_proxy(
                url,
                headers=headers,
                abort_if_timeout=abort_if_timeout,
                method=method,
                log_enabled=log_request
            )
        except Exception as e:
            if log_request:
                self.logger.info(f"Exception getting response from {url}: {str(e)}")
                self.logger.error("Error make_request:\n" + traceback.format_exc())
        return None

def load_links(path):
    links = set()
    if os.path.exists(path):
        with open(path, 'r') as file:
            links = set(line.strip() for line in file)
    return links

MAX_CONCURRENT_THREADS = 5
MAX_SLEEP_DELAY = 10

if __name__ == "__main__":
    try:
        scraper = WebsiteScraper(
                    crawled_links=load_links(r'coomerlinksHistory.txt'),
                    new_links_path=r'coomerlinksNewLinks.txt',
                    webdriver_pool = WebDriverPool(),
                    proxy_handler = ProxyHandler(startSearching=True),
                    timeout_threshold=60,
                    abortEarlyIfVisitedPageFully = True
                )

        scraper.parse_profiles()
        scraper.parse_queries()
        scraper.parse_api_posts()

    finally:
        logger.info("Finished crawling coomer")