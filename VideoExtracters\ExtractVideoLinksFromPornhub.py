import subprocess
import time
import os
import re
import json
import hashlib
from WebDriverPool import *
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
import io
import sys
import requests
import re
import tempfile
from collections import deque
import subprocess
from logger_helper import setup_logger

# Re-encode stdout and stderr to UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# Get the directory of the current script and change the working directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Get the root logger
logger = setup_logger(__name__, log_file='pornhub_scraper.log', level=logging.INFO)

# Tags to extract (leave empty to disable filtering)
TAGS_TO_EXTRACT = [
'2 girl blowjob',
'2 girls',
'3 girls',
'3some',
'3way',
'4some',
'cocksharing',
'cuckquean',
'cuckqueen',
'double bj',
'double blowjob',
'double cock sucking',
'double deepthroat',
'double facial',
'doubleblowjob',
'fffm',
'ffm',
'foursome',
'girls sharing',
'girls suck cock',
'mff',
'orgy',
'reverse gangbang',
'sharing cock',
'three girls blowjob',
'threesome',
'trio',
'triple bj',
'triple blowjob',
'two girls',
'twogirlsonecock',
]


pool = WebDriverPool()
logger.info("WebDriverPool initialized")

MAX_BACKOFF_DELAY = 60

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Referer': 'https://www.pornhub.com/',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Cache-Control': 'max-age=0'
}
logger.info("HTTP headers configured")

# Cache file for resolved URLs
URL_CACHE_FILE = 'pornhub_url_cache.json'
url_cache = {}

# Load URL cache from file at startup
def load_url_cache():
    global url_cache
    try:
        with open(URL_CACHE_FILE, 'r', encoding='utf-8') as f:
            url_cache = json.load(f)
        logger.info(f"URL cache loaded from {URL_CACHE_FILE} with {len(url_cache)} entries.")
    except FileNotFoundError:
        url_cache = {}
        logger.info("URL cache file not found. Starting with an empty cache.")
    except json.JSONDecodeError:
        url_cache = {}
        logger.warning("URL cache file is corrupted or empty. Starting with an empty cache.")

# Save URL cache to file
def save_url_cache():
    try:
        with open(URL_CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump(url_cache, f, indent=4)
        logger.info(f"URL cache saved to {URL_CACHE_FILE} with {len(url_cache)} entries.")
    except Exception as e:
        logger.error(f"Error saving URL cache to {URL_CACHE_FILE}: {e}")

load_url_cache() # Load cache at script start
def parse_html_files():
    logger.info("Starting parse_html_files")
    all_urls = set()
    for root, _, files in os.walk("pornhub_history"):  # Recursively walk through all subdirectories
        for filename in files:
            if filename.endswith('.html'):
                file_path = os.path.join(root, filename)
                logger.info(f"Processing HTML file: {file_path}")
                with open(file_path, 'r', encoding='utf-8') as file:
                    soup = BeautifulSoup(file, 'html.parser')

                    # Extract URLs from all 'a' tags with 'href' attributes
                    for a_tag in soup.find_all('a', href=True):
                        url = a_tag['href']
                        url = get_resolved_url_requests(url)
                        all_urls.add(url)
                        logger.debug(f"Extracted URL: {url} from {file_path}")
    logger.info(f"Finished parse_html_files, found {len(all_urls)} URLs")
    return all_urls

def get_resolved_url_requests(url):
    if url in url_cache:
        return url_cache[url]

    try:
        oldUrl = url
        url = url.replace("rt.pornhub.com", "pornhub.com")
        url = re.sub(r"&.*", "", url)
        url = re.sub(r"#.*", "", url)
        response = requests.head(url, headers=headers, allow_redirects=True, timeout=30)
        resolved_url = response.url
        if resolved_url != oldUrl:
            logger.info(f"Resolved URL: {resolved_url} for {oldUrl}")
        url_cache[url] = resolved_url # Store in cache
        save_url_cache() # Save cache after each resolution
        return resolved_url
    except Exception as e:
        logger.error(f"Error resolving URL {url}: {e}")
        url_cache[url] = url # Cache even error case to avoid repeated errors. Consider if this is desired behavior.
        save_url_cache()
        return url

def is_year_or_more(added_time):
    try:
        s = added_time.lower()
    except Exception:
        return False
    patterns = [
        r"([0-9]+)\s+year",
        r"([0-9]+)\s+years",
        r"([0-9]+)\s+год",
        r"([0-9]+)\s+года",
        r"([0-9]+)\s+лет",
    ]
    for pat in patterns:
        m = re.search(pat, s)
        if m and int(m.group(1)) >= 1:
            return True
    return False

def get_video_url_and_duration(url):
    logger.debug(f"Getting video URL and duration for URL: {url}")
    try:
        # Use yt-dlp to get video information in JSON format, including the best direct format
        command = ["yt-dlp", "-f", "bestvideo+bestaudio/best", "--dump-single-json", url]
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()

        if process.returncode != 0:
            error = stderr.decode("utf-8").strip()
            logger.error(f"Error getting video information: {error}")
            return None, None

        output = stdout.decode("utf-8").strip()

        # Parse the JSON output
        try:
            video_data = json.loads(output)
        except json.JSONDecodeError:
            logger.error("Error: Could not parse JSON output from yt-dlp.")
            return None, None

        # Find the best direct URL (if available)
        direct_url = None
        for fmt in video_data.get("formats", []):
            if fmt.get("protocol") == "https" and not fmt.get("url", "").endswith(".m3u8"):
                direct_url = fmt.get("url")
                break

        # Use the 'url' field as a fallback if no direct format is found
        if not direct_url:
            direct_url = video_data.get("url")

        # Extract the duration
        duration = video_data.get("duration")

        if duration is None:
            logger.warning(f"Warning: Duration not found in yt-dlp output for {url}. You may need to use other methods to determine duration.")
            duration = 0

        logger.info(f"Retrieved direct URL {direct_url} and duration {duration} for {url}")
        return direct_url, float(duration)

    except FileNotFoundError:
        logger.error("Error: yt-dlp is not installed or not found in PATH.")
        return None, None
    except Exception as e:
        logger.error(f"An error occurred while getting video URL and duration: {e}")
        return None, None

def process_video_data(video_data, porn_urls, scrapped_urls, screenshot_dir, retry_queue):
    logger.debug(f"Processing video data: {video_data.get('page_url', 'Unknown URL')}")
    max_retries = 5
    retries = 0
    try:
        while retries < max_retries:
            retries += 1
            page_url = video_data['page_url']
            video_url, video_duration = get_video_url_and_duration(page_url)
            logger.info(f"{page_url} - Found video URL {video_url}")
            logger.info(f"Retrieved duration: {video_duration}s for video URL: {video_url}")

            if video_url:
                try:
                    if video_duration:
                        if int(video_duration) < 300:
                            logger.info(f"Skipping {video_url} due to duration: {video_duration}")
                            return None

                        logger.info(f"Successfully retrieved duration: {video_duration}s for video URL: {video_url}")
                        timestamps = [int(video_duration * 0.2), int(video_duration * 0.5), int(video_duration * 0.8)]
                        screenshot_paths = []

                        for i, timestamp in enumerate(timestamps):
                            video_url_hash = hashlib.sha256(video_url.encode()).hexdigest()
                            screenshot_filename = f'screenshot_{i + 1}_{video_url_hash}.png'
                            screenshot_path = os.path.join(screenshot_dir, screenshot_filename)
                            capture_screenshot(video_url, timestamp, screenshot_path)
                            screenshot_paths.append(screenshot_path)
                            logger.info(f"Screenshot captured at {timestamp}s for video URL: {video_url} saved to {screenshot_path}")

                        video_data['screenshot_paths'] = screenshot_paths
                        # Check if all screenshots are identical
                        if len(set(open(path, 'rb').read() for path in screenshot_paths)) == len(screenshot_paths):
                            logger.debug(f"Screenshots are unique for {video_url}")
                            return video_data


                        if retries == max_retries:
                            logger.error(f"{page_url} - Maximum retries reached. Unable to capture unique screenshots.")
                            return None

                        logger.warning(f"{page_url} - Identical screenshots detected. Adding to retry queue.")
                        retry_queue.append(video_data)  # Add to the queue for later processing
                        return None

                    else:
                        video_data['screenshot_paths'] = []
                        logger.info(f"Video duration too short or could not be determined for {video_url}, skipping screenshots.")

                except Exception as e:
                    logger.error(f"Error processing video URL {video_url}: {e}")
                    return None

        return video_data

    except Exception as e:
        logger.error(f"An error occurred while processing video data: {e}")
        return None

def extract_tags_from_url(url, timeout=60, max_retries=3):
    html_content = pool.get_source(url, selector="div.video-detailed-info", button_selector="button.gtm-event-age-verification.js-closeAgeModal.orangeButton.buttonOver18", timeout=timeout, max_retries=max_retries)
    all_tags = set()
    if html_content:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Find both potential wrappers for tags and categories
        categories_wrapper = soup.find('div', class_='categoriesWrapper')
        tags_wrapper = soup.find('div', class_='tagsWrapper')

        wrappers = []
        if categories_wrapper:
            wrappers.append(categories_wrapper)
        if tags_wrapper:
            wrappers.append(tags_wrapper)

        for wrapper in wrappers:
            for tag in wrapper.find_all('a'):
                tag_text = tag.text.strip().lower()
                if tag_text:  # Ensure not empty
                    all_tags.add(tag_text)
        
        logger.info(f"{url} - extracted tags: {list(all_tags)}")
        
    return list(all_tags)

def get_pornhub_recommended_data(url, porn_urls, scrapped_urls, screenshot_dir, output_dir, retry_queue, debug=False):
    logger.info(f"Fetching recommended data for URL: {url}")
    try:
        logger.info(f"Fetching page source for URL: {url}")
        html_content = pool.get_source(url, selector="div#under-player-comments", button_selector="button.gtm-event-age-verification.js-closeAgeModal.orangeButton.buttonOver18")
        if not html_content:
            logger.warning(f"Could not retrieve HTML content for {url}")
            return None

        soup = BeautifulSoup(html_content, 'html.parser')

        video_items = soup.find_all('li', class_='pcVideoListItem')

        video_data_list = []

        logger.info(f"{url} has {len(video_items)} recommendations")

        for item in video_items:
            if not item.find('a', class_='linkVideoThumb'):
                logger.debug(f"Skipping item without linkVideoThumb class for url: {url}")
                continue

            video_data = {}

            link_tag = item.find('a', class_='linkVideoThumb')
            if link_tag and link_tag.has_attr('href'):
                href = link_tag['href']
                if href.startswith('/'):
                    href = 'https://www.pornhub.com' + href

                if "?viewkey=" not in href:
                    logger.debug(f"Skipping due to missing viewkey for url {url}: {href}")
                    continue

                added_tag = item.find('var', class_='added')
                if added_tag:
                    added_time = added_tag.text.strip()
                    if "55 лет назад" == added_time or "55 years ago" == added_time:
                        html_content2 = pool.get_source(href, selector="div.videoInfo", button_selector="button.gtm-event-age-verification.js-closeAgeModal.orangeButton.buttonOver18")
                        soup2 = BeautifulSoup(html_content2, 'html.parser')
                        videoInfo = soup2.find('div', class_='videoInfo')
                        added_time = videoInfo.text.strip()

                    if is_year_or_more(added_time):
                        logger.info(f"Skipping due to age >= 1 year: {href} - {added_time}")
                        continue

                    video_data['added_time'] = added_time
                    logger.info(f"{href} - extracted added time: {video_data['added_time']}")
                else:
                    logger.info(f"Found no added time: {item.prettify()}")


                is_studio = item.find('span', class_='channel-icon')
                if is_studio:
                    logger.info(f"Skipping due to being studio video: {href}")
                    continue

                resolved_href = get_resolved_url_requests(href)
                if resolved_href not in porn_urls and resolved_href not in scrapped_urls:
                    porn_urls.add(resolved_href)
                    scrapped_urls.add(resolved_href)
                    video_data['page_url'] = resolved_href
                    logger.debug(f"Added to porn urls and scrapped urls: {resolved_href}")

                    tags = extract_tags_from_url(resolved_href)
                    video_data['tags'] = tags

                    # Filter by tags
                    if TAGS_TO_EXTRACT:
                        if not tags:
                            logger.info(f"Skipping {resolved_href} because it has no tags to filter by.")
                            continue
                        if not any(tag in TAGS_TO_EXTRACT for tag in tags):
                            logger.info(f"Skipping {resolved_href} because it does not have the desired tags.")
                            continue
                else:
                    logger.info(f"Skipping duplicate or previously scrapped URL: {resolved_href}")
                    continue

                duration_tag = item.find('var', class_='duration')
                if duration_tag:
                    video_data['duration'] = duration_tag.text.strip()
                    logger.info(f"{href} - extracted duration: {video_data['duration']}")

                channel_block = item.find('div', class_='usernameWrap')
                if channel_block:
                    channel_tag = channel_block.find('a')
                    if channel_tag:
                        video_data['channel'] = channel_tag.text.strip()
                        logger.info(f"{href} - extracted channel: {video_data['channel']}")

                img_tag = item.find('img')
                if img_tag and img_tag.has_attr('data-mediumthumb'):
                    video_data['thumbnail_url'] = img_tag['data-mediumthumb']
                    logger.info(f"{href} - extracted thumbnail URL: {video_data['thumbnail_url']}")

                title_block = item.find('span', class_='title')
                if title_block:
                    title_tag = title_block.find('a')
                    if title_tag:
                        video_data['title'] = title_tag.text.strip()
                        logger.info(f"{href} - extracted title: {video_data['title']}")

                video_data = process_video_data(video_data, porn_urls, scrapped_urls, screenshot_dir, retry_queue)
                if not video_data:
                    logger.debug(f"Skipping video data due to error on {resolved_href}")
                    continue

            if video_data:
                video_data_list.append(video_data)
            if debug and len(scrapped_urls) >= 10:
                logger.info("Debug mode: Stopping after 10 URLs")
                return video_data_list
        logger.info(f"Finished getting recommended data for {url}. Found {len(video_data_list)} recommendations.")
        return video_data_list

    except Exception as e:
        logger.error(f"An error occurred: {e}")
        return None

def process_porn_urls(file_path):
    logger.info(f"Processing porn URLs from: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as input_file:
            lines = input_file.readlines()
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return set()
    except Exception as e:
        logger.error(f"Error processing file: {file_path} - {e}")
        return set()

    # Deduplicate lines before processing
    unique_lines = set(lines)
    logger.info(f"Got {len(unique_lines)} unique URLs from {file_path}")
    processed_lines = set()
    for line in unique_lines:
        if "pornhub" in line:
            url = re.sub(r'.*" "', '', re.sub(r"'\)", '', line)).replace('"', '').strip()
            resolved_url = get_resolved_url_requests(url)
            processed_lines.add(resolved_url)

    logger.info(f"Processed {len(processed_lines)} URLs from {file_path}")
    return processed_lines


def capture_screenshot(video_url, timestamp, output_path, max_retries=10, delay=5):
    logger.debug(f"Capturing screenshot for {video_url} at {timestamp}, saving to {output_path}")
    retries = 0
    while retries < max_retries:
        try:
            command = [
                'ffmpeg', '-hide_banner', '-loglevel', 'error', '-y',
                '-ss', str(timestamp),
                '-i', video_url,
                '-vframes', '1',
                output_path
            ]
            logger.info(f"Running screenshot command: {' '.join(command)}")
            subprocess.run(command, check=True, timeout=120)
            logger.info(f"Screenshot captured for {video_url} at {timestamp} to {output_path}")
            break
        except subprocess.CalledProcessError as e:
            logger.error(f"Error capturing screenshot (attempt {retries + 1}) for {video_url} at {timestamp}: {e}")
            retries += 1
            time.sleep(delay)
            delay = min(delay * 2, MAX_BACKOFF_DELAY)
        except subprocess.TimeoutExpired:
            logger.error(f"Timeout expired while capturing screenshot (attempt {retries + 1}) for {video_url} at {timestamp}")
            retries += 1
            time.sleep(delay)
            delay = min(delay * 2, MAX_BACKOFF_DELAY)
        except Exception as e:
            logger.error(f"Unexpected error during screenshot capture for {video_url} at {timestamp}: {e}")
            retries += 1
            time.sleep(delay)
            delay = min(delay * 2, MAX_BACKOFF_DELAY)
    if retries == max_retries:
        logger.error(f"Failed to capture screenshots from {video_url} after {max_retries} retries")

def extract_tags_from_file(file_path):
    logger.info(f"Starting tag extraction from file: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            urls = f.readlines()
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return

    unique_urls = set()
    for url in urls:
        url = url.strip()
        if url:
            unique_urls.add(url)
    
    logger.info(f"Found {len(unique_urls)} unique URLs to process.")

    for url in unique_urls:
        tags = extract_tags_from_url(url, timeout=1, max_retries=1)
        print(f"URL: {url}")
        print(f"Tags: {', '.join(tags) if tags else 'No tags found'}")
        print("-" * 20)

    logger.info("Tag extraction completed.")

def create_html_page(video_data_batch, output_file):
    logger.info(f"Creating HTML page: {output_file} with {len(video_data_batch)} video data items")
    try:
        html_content = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>pornhub Data</title>
            <style>
                .video-item {
                    border: 1px solid #ddd;
                    padding: 1px;
                    margin-bottom: 1px;
                    display: flex; /* Enable Flexbox */
                    align-items: stretch; /* Make both sides the same height */
                }
                .video-preview {
                    width: 20%;
                    padding: 0; /* Removed padding */
                    margin: 0; /* Removed margin */
                }
                .video-preview img {
                    max-width: 100%;
                    height: auto;
                }
                .video-details {
                    margin-bottom: 1px;
                }
                .screenshots-container {
                    width: 80%;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: flex-start; /* Align items to the start of the container */
                    justify-content: flex-start; /* Align items to the start of the line */
                    overflow: hidden; /* Prevent potential overflow */
                }
                .screenshots-container img {
                    max-width: calc(33.33% - 10px); /* Adjust as needed for spacing */
                    margin: 5px;
                    height: auto; /* Maintain aspect ratio */
                }

                /* Reduce space for video titles */
                .video-preview h3 {
                    font-size: 1em; /* Reduce font size */
                    margin-top: 0.2em; /* Reduce top margin */
                    margin-bottom: 0.2em; /* Reduce bottom margin */
                    padding: 0; /* Remove padding */
                    line-height: 1.2; /* Adjust line height for tighter spacing */
                }
                .video-preview a {
                    display: block; /* Make the link behave like a block element to control title spacing better */
                }

                /* Reduce space for video details (channel info) */
                .video-details p {
                    font-size: 0.9em; /* Slightly smaller font size than title */
                    margin-top: 0.1em; /* Reduce top margin */
                    margin-bottom: 0.1em; /* Reduce bottom margin */
                    padding: 0; /* Remove padding */
                    line-height: 1.1; /* Slightly tighter line height */
                }
            </style>
        </head>
        <body>
            <h1>pornhub Data</h1>
        """
        for video_data in video_data_batch:
            html_content += f"""
            <div class="video-item">
                <div class="video-preview">
                    <a href="{video_data.get('page_url', '#')}" target="_blank"><h3>{video_data.get('title', 'No Title')}</h3></a>
                    <img src="{video_data.get('thumbnail_url', '')}" alt="Thumbnail">
                    <div class="video-details">
                        <p><strong>Channel:</strong> {video_data.get('channel', 'N/A')}</p>
                        <p><strong>Added Time:</strong> {video_data.get('added_time', 'N/A')}</p>
                    </div>
                </div>
                <div class="screenshots-container">
            """
            for screenshot_path in video_data.get('screenshot_paths', []):
                if screenshot_path and os.path.exists(screenshot_path):
                    # Use relative path in HTML
                    screenshot_filename = os.path.basename(screenshot_path)
                    html_content += f'<img src="screenshots/{screenshot_filename}" alt="Video Screenshot">'

            html_content += """
                </div>
            </div>
            """
        html_content += """
        </body>
        </html>
        """

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        logger.info(f"HTML page saved to '{output_file}'")
    except Exception as e:
        logger.error(f"Error creating HTML page {output_file}: {e}")

def run_scraper():
    logger.info("Script execution started in scrape mode.")
    history_urls = parse_html_files()
    scrapped_urls = set()
    scrapped_urls.update(history_urls)
    logger.info(f"Loaded {len(scrapped_urls)} scrapped URLs from history.")
    porn_urls = process_porn_urls("PornUrls.txt")
    urls_to_process = list(porn_urls)
    logger.info(f"Got {len(urls_to_process)} URLs to process from PornUrls.txt")
    all_video_data = []
    retry_queue = deque()
    batch_size = 100
    batch_count = 0
    debug_mode = False # set to true for debug mode

    # Create a directory for screenshots and the output directory
    output_dir = "pornhub_output_screenshots"
    screenshot_dir = os.path.join(output_dir, "screenshots")
    os.makedirs(screenshot_dir, exist_ok=True)
    logger.info(f"Screenshots will be saved to: {screenshot_dir}")

    for video_page_url in urls_to_process:
        try:
           recommended_video_data = get_pornhub_recommended_data(video_page_url, porn_urls, scrapped_urls, screenshot_dir, output_dir, retry_queue, debug=debug_mode)
           if recommended_video_data:
             all_video_data.extend(recommended_video_data)
             logger.info(f"Added {len(recommended_video_data)} video data items from {video_page_url} to the main data list")

             # Check if a batch is formed and generate HTML
             if len(all_video_data) >= batch_size:
                 batch_count += 1
                 video_data_batch = all_video_data[:batch_size]
                 output_file = os.path.join(output_dir, f'pornhub_data_batch_{batch_count}.html')
                 create_html_page(video_data_batch, output_file)
                 # Clear the processed batch
                 all_video_data = all_video_data[batch_size:]
                 logger.info(f"Processed batch {batch_count}. Created html page at {output_file}, cleared {batch_size} items from main video data list")

           else:
                logger.warning(f"Could not retrieve recommended video data for {video_page_url}")
        except Exception as e:
             logger.error(f"Error processing main page URL {video_page_url}: {e}")

        if debug_mode and len(all_video_data) >=10:
            logger.info("Debug Mode: Stopping after processing 10 URLs")
            break

    # Process the retry queue
    logger.info(f"Processing retry queue with {len(retry_queue)} items")
    while retry_queue:
        video_data = retry_queue.popleft()
        logger.info(f"Retrying video data processing for URL: {video_data.get('page_url', 'Unknown URL')}")
        processed_data = process_video_data(video_data, porn_urls, scrapped_urls, screenshot_dir, deque())
        if processed_data:
            all_video_data.append(processed_data)
            logger.info(f"Successfully processed video data during retry for: {video_data.get('page_url', 'Unknown URL')}")
        else:
            logger.warning(f"Failed to process video data after retry: {video_data.get('page_url', 'Unknown URL')}")
        # Check if a batch is formed and generate HTML
        if len(all_video_data) >= batch_size:
            batch_count += 1
            video_data_batch = all_video_data[:batch_size]
            output_file = os.path.join(output_dir, f'pornhub_data_batch_{batch_count}.html')
            create_html_page(video_data_batch, output_file)
            # Clear the processed batch
            all_video_data = all_video_data[batch_size:]
            logger.info(f"Processed batch {batch_count}. Created html page at {output_file} from the retry queue, cleared {batch_size} items from main video data list")

    # Process any remaining data after the loop
    if all_video_data:
        batch_count += 1
        output_file = os.path.join(output_dir, f'pornhub_data_batch_{batch_count}.html')
        create_html_page(all_video_data, output_file)
        logger.info(f"Processed remaining data. Created html page at {output_file}")

    logger.info(f"HTML pages created. Total batches: {batch_count}")
    logger.info("Script execution completed.")

def run_tag_extractor():
    extract_tags_from_file("pornhub_links.txt")

if __name__ == '__main__':
    run_scraper()
    #run_tag_extractor()